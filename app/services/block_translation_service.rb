# Služba pro překlad textového obsahu bloků pomocí Mobility gem
# Nepřekládá vizuální nastavení, pouze textový obsah v BlockControl
class BlockTranslationService
  attr_reader :block, :target_locale, :source_locale

  def initialize(block, target_locale, source_locale = nil)
    @block = block
    @target_locale = target_locale.to_s
    @source_locale = source_locale&.to_s || I18n.default_locale.to_s
  end

  # Hlavní metoda pro překlad bloku
  # @return [Boolean] úspěch překladu
  def call
    Rails.logger.info "🔄 Začínám překlad bloku #{@block.id} z #{@source_locale} do #{@target_locale}"
    
    return false unless valid_translation_request?

    ActiveRecord::Base.transaction do
      translate_block_controls
    end

    Rails.logger.info "✅ Překlad bloku #{@block.id} dokončen úspěšně"
    true
  rescue => e
    Rails.logger.error "❌ Chyba při překladu bloku #{@block.id}: #{e.message}"
    Rails.logger.error "📍 Backtrace: #{e.backtrace.first(5).join("\n")}"
    false
  end

  # Přeloží všechny BlockControls v bloku
  def translate_block_controls
    @block.controls.each do |control|
      translate_block_control(control)
    end
  end

  # Přeloží jeden BlockControl
  def translate_block_control(control)
    return unless control.text.present?

    Rails.logger.info "📝 Překládám BlockControl #{control.id}: #{control.text.truncate(50)}"

    # Získej text v source locale
    source_text = nil
    Mobility.with_locale(@source_locale) do
      source_text = control.text
    end

    return unless source_text.present?

    # Přelož text pomocí DeepL
    translated_text = translate_text(source_text)
    return unless translated_text.present?

    # Ulož překlad do target locale
    Mobility.with_locale(@target_locale) do
      control.text = translated_text
      control.save!
    end

    Rails.logger.info "✅ BlockControl #{control.id} přeložen: #{translated_text.truncate(50)}"
  end

  # Přeloží text pomocí DeepL API
  def translate_text(text)
    return text if text.blank?

    deepl_service = DeepLService.new
    translated_texts = deepl_service.translate([text], @source_locale, @target_locale)
    
    translated_texts.first
  rescue => e
    Rails.logger.warn "⚠️ Chyba při překladu textu: #{e.message}"
    # Fallback - vrátíme původní text s označením
    "#{text} [#{@target_locale.upcase}]"
  end

  # Získá všechny dostupné překlady pro blok
  def available_translations
    locales = Set.new

    @block.controls.each do |control|
      next unless control.text_translations.present?
      
      control.text_translations.keys.each do |locale|
        locales << locale if control.text_translations[locale].present?
      end
    end

    locales.to_a.sort
  end

  # Zkontroluje, zda blok má překlad v daném locale
  def has_translation?(locale)
    available_translations.include?(locale.to_s)
  end

  # Získá statistiky překladů pro blok
  def translation_stats
    stats = {}
    
    @block.controls.each do |control|
      next unless control.text.present?
      
      control.text_translations.each do |locale, text|
        next if text.blank?
        
        stats[locale] ||= { controls: 0, characters: 0 }
        stats[locale][:controls] += 1
        stats[locale][:characters] += text.length
      end
    end

    stats
  end

  private

  # Ověří, zda je požadavek na překlad validní
  def valid_translation_request?
    unless @block
      Rails.logger.error "❌ Blok nenalezen"
      return false
    end

    if @source_locale == @target_locale
      Rails.logger.error "❌ Zdrojový a cílový jazyk jsou stejné: #{@source_locale}"
      return false
    end

    unless Website::AVAILABLE_LOCALES.include?(@target_locale)
      Rails.logger.error "❌ Neplatný cílový jazyk: #{@target_locale}. Dostupné: #{Website::AVAILABLE_LOCALES}"
      return false
    end

    unless Website::AVAILABLE_LOCALES.include?(@source_locale)
      Rails.logger.error "❌ Neplatný zdrojový jazyk: #{@source_locale}. Dostupné: #{Website::AVAILABLE_LOCALES}"
      return false
    end

    controls_with_text = @block.controls.select { |c| c.text.present? }
    if controls_with_text.empty?
      Rails.logger.info "ℹ️ Blok #{@block.id} nemá žádné textové ovládací prvky k překladu"
      return false
    end

    Rails.logger.info "✅ Validace úspěšná - blok má #{controls_with_text.count} textových ovládacích prvků"
    true
  end
end
