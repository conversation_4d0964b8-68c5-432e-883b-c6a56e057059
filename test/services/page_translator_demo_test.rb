require "test_helper"

class PageTranslatorDemoTest < Minitest::Test
  def test_deep_l_service_works
    service = DeepLService.new
    texts = ["Ahoj", "Jak se máš?"]
    translated = service.translate(texts, "cs", "en")
    
    assert_equal 2, translated.length
    assert_equal "Ahoj_translated_en", translated[0]
    assert_equal "Jak se máš?_translated_en", translated[1]
    
    puts "✅ DeepLService funguje správně"
    puts "   Původní texty: #{texts.inspect}"
    puts "   Přeložené texty: #{translated.inspect}"
  end

  def test_page_translator_service_basic_functionality
    # Test základní logiky bez databáze
    service_class = PageTranslatorService

    # Ově<PERSON>, že třída existuje a má správné metody
    assert service_class.respond_to?(:new)

    # Test, že má správné instance metody (bez vytváření instance)
    instance_methods = service_class.instance_methods(false)
    assert_includes instance_methods, :call

    puts "✅ PageTranslatorService má správnou strukturu"
    puts "   Třída: #{service_class}"
    puts "   Instance metody: #{instance_methods.grep(/call|translate|clone|duplicate/).sort}"
  end

  def test_page_model_has_translation_associations
    # Test, že Page model má správné asociace
    page_class = Page
    
    # Ověř, že má správné asociace
    associations = page_class.reflect_on_all_associations.map(&:name)
    
    assert_includes associations, :original_page
    assert_includes associations, :translations
    
    puts "✅ Page model má správné asociace pro překlady"
    puts "   Asociace: #{associations.grep(/translation|original/).sort}"
  end

  def test_block_model_has_translation_associations
    # Test, že Block model má správné asociace
    block_class = Block
    
    # Ověř, že má správné asociace
    associations = block_class.reflect_on_all_associations.map(&:name)
    
    assert_includes associations, :original_block
    assert_includes associations, :translated_blocks
    
    puts "✅ Block model má správné asociace pro překlady"
    puts "   Asociace: #{associations.grep(/translated|original/).sort}"
  end

  def test_friendly_id_scope_includes_locale
    # Test, že friendly_id má správný scope
    page_class = Page
    
    # Ověř friendly_id konfiguraci
    friendly_id_config = page_class.friendly_id_config
    
    assert_includes friendly_id_config.scope, :website_id
    assert_includes friendly_id_config.scope, :locale
    
    puts "✅ Page model má správný friendly_id scope"
    puts "   Scope: #{friendly_id_config.scope}"
  end

  def test_website_has_available_locales
    # Test, že Website má správné locale konstanty
    website_class = Website
    
    assert_includes website_class::AVAILABLE_LOCALES, "cs"
    assert_includes website_class::AVAILABLE_LOCALES, "en"
    assert_includes website_class::AVAILABLE_LOCALES, "de"
    assert_includes website_class::AVAILABLE_LOCALES, "pl"
    
    puts "✅ Website má správné dostupné jazyky"
    puts "   Dostupné jazyky: #{website_class::AVAILABLE_LOCALES}"
  end

  def test_block_layer_configs_exist
    # Test, že Block má správné LAYER_CONFIGS
    block_class = Block
    
    assert block_class.const_defined?(:LAYER_CONFIGS)
    
    layer_configs = block_class::LAYER_CONFIGS
    assert_includes layer_configs.keys, :outer_container_layer
    assert_includes layer_configs.keys, :inner_container_layer
    assert_includes layer_configs.keys, :content_layer
    
    puts "✅ Block model má správné LAYER_CONFIGS"
    puts "   Vrstvy: #{layer_configs.keys}"
  end
end
