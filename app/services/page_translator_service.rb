# Služba pro automatický překlad stránek a jejich obsahu
# Klonuje strán<PERSON>, dup<PERSON><PERSON><PERSON> bloky a ov<PERSON><PERSON><PERSON><PERSON><PERSON>, překládá texty
class PageTranslatorService
  attr_reader :original_page, :target_locale, :translated_page

  def initialize(original_page_id, target_locale)
    @original_page = Page.find(original_page_id)
    @target_locale = target_locale.to_s
    @translated_page = nil
  end

  # Hlavní metoda pro provedení překladu
  # @return [Page, nil] přelo<PERSON>ená stránka nebo nil při chybě
  def call
    return nil unless valid_translation_request?

    ActiveRecord::Base.transaction do
      clone_page
      duplicate_blocks_and_controls
      translate_all_texts
      save_translated_page
    end

    @translated_page
  rescue => e
    Rails.logger.error "Chyba při překladu stránky #{@original_page.id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    nil
  end

  private

  # <PERSON><PERSON><PERSON><PERSON><PERSON>, zda je požadavek na překlad validní
  def valid_translation_request?
    return false unless @original_page
    return false if @original_page.locale == @target_locale
    return false unless Website::AVAILABLE_LOCALES.include?(@target_locale)
    
    # Zkontroluj, zda překlad již neexistuje
    existing_translation = @original_page.translations.find_by(locale: @target_locale)
    if existing_translation
      Rails.logger.info "Překlad stránky #{@original_page.id} do #{@target_locale} již existuje (ID: #{existing_translation.id})"
      return false
    end
    
    true
  end

  # Klonuje původní stránku pro nový jazyk
  def clone_page
    @translated_page = Page.new(
      website_id: @original_page.website_id,
      title: @original_page.title, # Bude přeložen později
      heading: @original_page.heading, # Bude přeložen později
      meta_title: @original_page.meta_title, # Bude přeložen později
      meta_description: @original_page.meta_description, # Bude přeložen později
      type: @original_page.type,
      locale: @target_locale,
      translation_of_id: @original_page.id,
      ancestry: @original_page.ancestry,
      scope_id: @original_page.scope_id,
      template_id: @original_page.template_id,
      is_homepage: false, # Překlady obvykle nejsou homepage
      published_at: nil # Překlady začínají jako nepublikované
    )
    
    # Vygeneruj nový slug pro přeloženou stránku
    @translated_page.save!(validate: false) # Uložíme bez validace pro vygenerování slug
  end

  # Duplikuje všechny bloky a jejich ovládací prvky
  def duplicate_blocks_and_controls
    @original_page.blocks.each do |original_block|
      duplicate_block(original_block)
    end
  end

  # Duplikuje jeden blok a jeho ovládací prvky
  def duplicate_block(original_block)
    new_block = Block.new(
      page_id: @translated_page.id,
      name: original_block.name,
      position: original_block.position,
      options: original_block.options.deep_dup,
      media_options: original_block.media_options.deep_dup,
      pricing_options: original_block.pricing_options.deep_dup,
      pricing_id: original_block.pricing_id,
      media_collection_id: original_block.media_collection_id,
      media_type_id: original_block.media_type_id,
      translated_from_block_id: original_block.id
    )
    
    new_block.save!
    
    # Duplikuj ovládací prvky bloku
    original_block.controls.each do |original_control|
      duplicate_block_control(original_control, new_block)
    end
    
    # Duplikuj media pokud existují
    duplicate_block_media(original_block, new_block)
    
    new_block
  end

  # Duplikuje ovládací prvek bloku
  def duplicate_block_control(original_control, new_block)
    new_control = BlockControl.new(
      block_id: new_block.id,
      type: original_control.type,
      text: original_control.text, # Bude přeložen později
      position: original_control.position,
      options: original_control.options.deep_dup,
      classes: original_control.classes.deep_dup,
      styles: original_control.styles.deep_dup,
      content: original_control.content.deep_dup,
      container: original_control.container
    )
    
    new_control.save!
    new_control
  end

  # Duplikuje media asociovaná s blokem
  def duplicate_block_media(original_block, new_block)
    original_block.media.each do |original_media|
      new_media = Media.new(
        block_id: new_block.id,
        website_id: @translated_page.website_id,
        title: original_media.title, # Bude přeložen později
        text: original_media.text, # Bude přeložen později
        content: original_media.content.deep_dup,
        data: original_media.data.deep_dup,
        position: original_media.position,
        origin: original_media.origin,
        unique_id: original_media.unique_id,
        published_at: original_media.published_at,
        image_id: original_media.image_id,
        icon_id: original_media.icon_id,
        media_collection_id: original_media.media_collection_id,
        media_type_id: original_media.media_type_id
      )
      
      new_media.save!
    end
  end

  # Přeloží všechny texty na stránce a jejích blocích
  def translate_all_texts
    texts_to_translate = extract_translatable_texts
    return if texts_to_translate.empty?
    
    translated_texts = DeepLService.new.translate(
      texts_to_translate, 
      @original_page.locale, 
      @target_locale
    )
    
    apply_translated_texts(translated_texts)
  end

  # Extrahuje všechny přeložitelné texty
  def extract_translatable_texts
    texts = []
    
    # Texty ze stránky
    texts << @translated_page.title if @translated_page.title.present?
    texts << @translated_page.heading if @translated_page.heading.present?
    texts << @translated_page.meta_title if @translated_page.meta_title.present?
    texts << @translated_page.meta_description if @translated_page.meta_description.present?
    
    # Texty z bloků a ovládacích prvků
    @translated_page.blocks.each do |block|
      # Texty z options bloků (vnořené v layer_attributes)
      extract_texts_from_block_options(block, texts)
      
      # Texty z ovládacích prvků
      block.controls.each do |control|
        texts << control.text if control.text.present?
        extract_texts_from_control_options(control, texts)
      end
      
      # Texty z media
      block.media.each do |media|
        texts << media.title if media.title.present?
        texts << media.text if media.text.present?
      end
    end
    
    texts.compact
  end

  # Extrahuje texty z options bloku (z layer_attributes)
  def extract_texts_from_block_options(block, texts)
    return unless block.options.is_a?(Hash)
    
    Block::LAYER_CONFIGS.each_key do |layer_key|
      attributes_key = "#{layer_key}_attributes"
      layer_attrs = block.options[attributes_key]
      
      next unless layer_attrs.is_a?(Hash)
      
      # Hledej textové atributy v layer_attributes
      layer_attrs.each do |key, value|
        if value.is_a?(String) && value.present? && text_attribute?(key)
          texts << value
        end
      end
    end
  end

  # Extrahuje texty z options ovládacího prvku
  def extract_texts_from_control_options(control, texts)
    return unless control.options.is_a?(Hash)
    
    control.options.each do |key, value|
      if value.is_a?(String) && value.present? && text_attribute?(key)
        texts << value
      end
    end
  end

  # Určuje, zda je atribut textový (měl by být přeložen)
  def text_attribute?(key)
    text_keys = %w[
      text content title heading description label placeholder
      primary_button_text secondary_button_text pre_header
    ]
    
    text_keys.any? { |text_key| key.to_s.include?(text_key) }
  end

  # Aplikuje přeložené texty zpět na stránku a její obsah
  def apply_translated_texts(translated_texts)
    text_index = 0
    
    # Aplikuj na stránku
    text_index = apply_page_translations(text_index, translated_texts)
    
    # Aplikuj na bloky a ovládací prvky
    @translated_page.blocks.each do |block|
      text_index = apply_block_translations(block, text_index, translated_texts)
    end
  end

  # Aplikuje překlady na stránku
  def apply_page_translations(text_index, translated_texts)
    if @translated_page.title.present?
      @translated_page.title = translated_texts[text_index]
      text_index += 1
    end
    
    if @translated_page.heading.present?
      @translated_page.heading = translated_texts[text_index]
      text_index += 1
    end
    
    if @translated_page.meta_title.present?
      @translated_page.meta_title = translated_texts[text_index]
      text_index += 1
    end
    
    if @translated_page.meta_description.present?
      @translated_page.meta_description = translated_texts[text_index]
      text_index += 1
    end
    
    text_index
  end

  # Aplikuje překlady na blok a jeho obsah
  def apply_block_translations(block, text_index, translated_texts)
    # Aplikuj na options bloku
    text_index = apply_block_options_translations(block, text_index, translated_texts)
    
    # Aplikuj na ovládací prvky
    block.controls.each do |control|
      text_index = apply_control_translations(control, text_index, translated_texts)
    end
    
    # Aplikuj na media
    block.media.each do |media|
      text_index = apply_media_translations(media, text_index, translated_texts)
    end
    
    text_index
  end

  # Aplikuje překlady na options bloku
  def apply_block_options_translations(block, text_index, translated_texts)
    return text_index unless block.options.is_a?(Hash)
    
    Block::LAYER_CONFIGS.each_key do |layer_key|
      attributes_key = "#{layer_key}_attributes"
      layer_attrs = block.options[attributes_key]
      
      next unless layer_attrs.is_a?(Hash)
      
      layer_attrs.each do |key, value|
        if value.is_a?(String) && value.present? && text_attribute?(key)
          layer_attrs[key] = translated_texts[text_index]
          text_index += 1
        end
      end
    end
    
    # Označí options jako změněné pro uložení
    block.options_will_change!
    
    text_index
  end

  # Aplikuje překlady na ovládací prvek
  def apply_control_translations(control, text_index, translated_texts)
    if control.text.present?
      control.text = translated_texts[text_index]
      text_index += 1
    end
    
    if control.options.is_a?(Hash)
      control.options.each do |key, value|
        if value.is_a?(String) && value.present? && text_attribute?(key)
          control.options[key] = translated_texts[text_index]
          text_index += 1
        end
      end
      control.options_will_change!
    end
    
    text_index
  end

  # Aplikuje překlady na media
  def apply_media_translations(media, text_index, translated_texts)
    if media.title.present?
      media.title = translated_texts[text_index]
      text_index += 1
    end
    
    if media.text.present?
      media.text = translated_texts[text_index]
      text_index += 1
    end
    
    text_index
  end

  # Uloží přeloženou stránku a všechen její obsah
  def save_translated_page
    @translated_page.save!
    
    @translated_page.blocks.each do |block|
      block.save!
      
      block.controls.each(&:save!)
      block.media.each(&:save!)
    end
  end
end
