# == Schema Information
#
# Table name: blocks
#
#  id                       :bigint           not null, primary key
#  hidden_at                :datetime
#  media_options            :jsonb
#  name                     :string
#  options                  :jsonb
#  position                 :integer
#  pricing_options          :jsonb
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  media_collection_id      :bigint
#  media_type_id            :bigint
#  page_id                  :bigint
#  pricing_id               :bigint
#  translated_from_block_id :bigint
#
# Indexes
#
#  index_blocks_on_media_collection_id       (media_collection_id)
#  index_blocks_on_media_type_id             (media_type_id)
#  index_blocks_on_pricing_id                (pricing_id)
#  index_blocks_on_translated_from_block_id  (translated_from_block_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_type_id => media_types.id)
#  fk_rails_...  (page_id => pages.id)
#  fk_rails_...  (pricing_id => pricing.id)
#  fk_rails_...  (translated_from_block_id => blocks.id)
#

one:
  page_id: 1
  name: MyString
  position: 1
  options: {}
  media_options: {}
  pricing_options: {}

two:
  page_id: 2
  name: MyString
  position: 1
  options: {}
  media_options: {}
  pricing_options: {}
