class CreatePageBlocks < ActiveRecord::Migration[8.0]
  def change
    create_table :page_blocks do |t|
      t.references :page, null: false, foreign_key: true
      t.references :block, null: false, foreign_key: true
      t.integer :position, default: 1, null: false

      t.timestamps
    end

    add_index :page_blocks, [:page_id, :position]
    add_index :page_blocks, [:page_id, :block_id], unique: true
  end
end
