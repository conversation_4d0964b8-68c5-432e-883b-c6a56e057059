require "test_helper"

class DeepLServiceTest < Minitest::Test
  def setup
    @service = DeepLService.new
  end

  def test_should_translate_array_of_texts
    texts = ["<PERSON>oj", "<PERSON><PERSON> se máš?", "Děkuji"]
    translated = @service.translate(texts, "cs", "en")

    assert_equal texts.length, translated.length
    assert_equal "Ahoj_translated_en", translated[0]
    assert_equal "Jak se máš?_translated_en", translated[1]
    assert_equal "Děkuji_translated_en", translated[2]
  end

  def test_should_handle_empty_array
    translated = @service.translate([], "cs", "en")
    assert_equal [], translated
  end

  def test_should_handle_nil_input
    translated = @service.translate(nil, "cs", "en")
    assert_equal [], translated
  end

  def test_should_handle_blank_strings
    texts = ["Ahoj", "", nil, "Děkuji"]
    translated = @service.translate(texts, "cs", "en")

    assert_equal 4, translated.length
    assert_equal "Ahoj_translated_en", translated[0]
    assert_equal "", translated[1]
    assert_nil translated[2]
    assert_equal "Děkuji_translated_en", translated[3]
  end

  def test_should_preserve_original_text_structure
    texts = ["Text s <strong>HTML</strong> tagy", "Text s\nnový řádek"]
    translated = @service.translate(texts, "cs", "en")

    assert_includes translated[0], "<strong>HTML</strong>"
    assert_includes translated[1], "\n"
  end
end
