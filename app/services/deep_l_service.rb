# Služba pro překlad textů pomocí DeepL API
class DeepLService
  API_URL = 'https://api-free.deepl.com/v2/translate'.freeze

  def initialize
    @api_key = '1f0750d7-da68-4a86-a584-085381cf5d22:fx'
  end

  # Přeloží pole textů z source_locale do target_locale
  # @param texts_array [Array<String>] pole textů k překladu
  # @param source_locale [String] z<PERSON><PERSON><PERSON><PERSON><PERSON> jaz<PERSON> (např. "cs")
  # @param target_locale [String] c<PERSON><PERSON><PERSON> jaz<PERSON> (např. "en")
  # @return [Array<String>] pole přeložených textů
  def translate(texts_array, source_locale, target_locale)
    return [] if texts_array.blank?

    # Filtruj prázdné texty ale zachovej pozice
    texts_with_indices = texts_array.each_with_index.reject { |text, _| text.blank? }
    return texts_array if texts_with_indices.empty?

    begin
      # Volej DeepL API pouze pro neprázdné texty
      non_empty_texts = texts_with_indices.map(&:first)
      translated_texts = call_deepl_api(non_empty_texts, source_locale, target_locale)

      # Rekonstruuj původní pole s přeloženými texty
      result = texts_array.dup
      texts_with_indices.each_with_index do |(_, original_index), translated_index|
        result[original_index] = translated_texts[translated_index]
      end

      result
    rescue => e
      Rails.logger.error "Chyba při překladu přes DeepL API: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      # Fallback na simulaci při chybě
      fallback_translate(texts_array, target_locale)
    end
  end

  private

  # Budoucí implementace pro skutečné volání DeepL API
  def call_deepl_api(texts, source_locale, target_locale)
    # Zde bude implementace skutečného volání DeepL API
    # require 'net/http'
    # require 'json'
    # 
    # uri = URI('https://api-free.deepl.com/v2/translate')
    # http = Net::HTTP.new(uri.host, uri.port)
    # http.use_ssl = true
    # 
    # request = Net::HTTP::Post.new(uri)
    # request['Authorization'] = "DeepL-Auth-Key #{@api_key}"
    # request['Content-Type'] = 'application/x-www-form-urlencoded'
    # 
    # params = {
    #   'text' => texts,
    #   'source_lang' => source_locale.upcase,
    #   'target_lang' => target_locale.upcase
    # }
    # request.body = URI.encode_www_form(params)
    # 
    # response = http.request(request)
    # result = JSON.parse(response.body)
    # 
    # result['translations'].map { |translation| translation['text'] }
  end
end
