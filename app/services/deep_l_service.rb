# Služba pro překlad textů pomocí DeepL API
class DeepLService
  API_URL = 'https://api-free.deepl.com/v2/translate'.freeze

  def initialize
    @api_key = '1f0750d7-da68-4a86-a584-085381cf5d22:fx'
  end

  # Přeloží pole textů z source_locale do target_locale
  # @param texts_array [Array<String>] pole textů k překladu
  # @param source_locale [String] z<PERSON><PERSON><PERSON><PERSON><PERSON> jaz<PERSON> (např. "cs")
  # @param target_locale [String] c<PERSON><PERSON><PERSON> jaz<PERSON> (např. "en")
  # @return [Array<String>] pole přeložených textů
  def translate(texts_array, source_locale, target_locale)
    return [] if texts_array.blank?

    # Filtruj prázdné texty ale zachovej pozice
    texts_with_indices = texts_array.each_with_index.reject { |text, _| text.blank? }
    return texts_array if texts_with_indices.empty?

    begin
      # Volej DeepL API pouze pro neprázdné texty
      non_empty_texts = texts_with_indices.map(&:first)
      translated_texts = call_deepl_api(non_empty_texts, source_locale, target_locale)

      # Rekonstruuj původní pole s přeloženými texty
      result = texts_array.dup
      texts_with_indices.each_with_index do |(_, original_index), translated_index|
        result[original_index] = translated_texts[translated_index]
      end

      result
    rescue => e
      Rails.logger.error "Chyba při překladu přes DeepL API: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      # Fallback na simulaci při chybě
      fallback_translate(texts_array, target_locale)
    end
  end

  private

  # Skutečné volání DeepL API
  def call_deepl_api(texts, source_locale, target_locale)
    require 'net/http'
    require 'json'

    uri = URI(API_URL)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.read_timeout = 30

    request = Net::HTTP::Post.new(uri)
    request['Authorization'] = "DeepL-Auth-Key #{@api_key}"
    request['Content-Type'] = 'application/x-www-form-urlencoded'

    # Mapování jazyků na DeepL formát
    source_lang = map_locale_to_deepl(source_locale)
    target_lang = map_locale_to_deepl(target_locale)

    # Připrav parametry pro DeepL API
    params = []
    params << ['source_lang', source_lang]
    params << ['target_lang', target_lang]
    params << ['preserve_formatting', '1']
    params << ['tag_handling', 'html']

    # Přidej každý text jako samostatný parametr
    texts.each { |text| params << ['text', text] }

    request.body = URI.encode_www_form(params)

    Rails.logger.info "Volám DeepL API: #{source_lang} -> #{target_lang}, #{texts.length} textů"

    response = http.request(request)

    unless response.code == '200'
      raise "DeepL API chyba: #{response.code} - #{response.body}"
    end

    result = JSON.parse(response.body)

    unless result['translations']
      raise "Neočekávaná odpověď z DeepL API: #{result}"
    end

    translated_texts = result['translations'].map { |translation| translation['text'] }

    Rails.logger.info "DeepL překlad úspěšný: #{translated_texts.length} textů přeloženo"

    translated_texts
  end

  # Mapování locale na DeepL jazykové kódy
  def map_locale_to_deepl(locale)
    case locale.to_s.downcase
    when 'cs'
      'CS'
    when 'en'
      'EN'
    when 'de'
      'DE'
    when 'pl'
      'PL'
    when 'sk'
      'SK'
    when 'fr'
      'FR'
    when 'es'
      'ES'
    when 'it'
      'IT'
    else
      locale.upcase
    end
  end

  # Fallback překlad při chybě API
  def fallback_translate(texts_array, target_locale)
    Rails.logger.warn "Používám fallback překlad pro #{target_locale}"

    texts_array.map do |text|
      next text if text.blank?
      "#{text}_translated_#{target_locale}"
    end
  end
end
