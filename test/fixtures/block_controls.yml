# == Schema Information
#
# Table name: block_controls
#
#  id                :bigint           not null, primary key
#  classes           :jsonb
#  container         :string
#  content           :jsonb
#  options           :jsonb
#  position          :integer          default(0)
#  styles            :jsonb
#  text              :text
#  text_translations :jsonb
#  type              :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  block_id          :bigint
#
# Indexes
#
#  index_block_controls_on_block_id           (block_id)
#  index_block_controls_on_text_translations  (text_translations) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#

one:
  block_id: 1
  type: BlockControls::Heading
  text: MyText
  styles: {}
  classes: {}
  options: {}
  content: {}
  position: 1
  container: content

two:
  block_id: 2
  type: BlockControls::Paragraph
  text: MyText
  styles: {}
  classes: {}
  options: {}
  content: {}
  position: 1
  container: content
