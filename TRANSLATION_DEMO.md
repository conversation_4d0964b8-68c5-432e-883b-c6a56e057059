# Demonstrace automatického překladu stránek

Tato implementace poskytuje základní strukturu pro automatický překlad stránek a jejich obsahu v Ruby on Rails aplikaci.

## Implementované funkce

### 1. Databázová struktura
- ✅ <PERSON><PERSON><PERSON><PERSON> sloupec `translation_of_id` do tabulky `pages`
- ✅ <PERSON><PERSON><PERSON><PERSON> sloupec `translated_from_block_id` do tabulky `blocks`
- ✅ Vytvořeny foreign key constraints pro referenční integritu

### 2. Model asociace
- ✅ Page model má asociace `original_page` a `translations`
- ✅ Block model má asociace `original_block` a `translated_blocks`
- ✅ Friendly_id scope rozšířen o `locale` pro unikátní slugy per jazyk

### 3. Služby
- ✅ **DeepLService** - simulace překladové služby (přidává suffix `_translated_{locale}`)
- ✅ **PageTranslatorService** - hlavní logika klonování a překladu

### 4. Funkčnost PageTranslatorService

#### Klonování stránky
- Vytvoří novou stránku pro cílový jazyk
- Zkopíruje všechny relevantní atributy
- Nastaví `translation_of_id` na původní stránku
- Vygeneruje unikátní slug pro nový jazyk

#### Duplikace bloků
- Klonuje všechny bloky původní stránky
- Zachová strukturu `options` JSONB (včetně layer_attributes)
- Nastaví `translated_from_block_id` pro sledování původu

#### Duplikace ovládacích prvků
- Klonuje všechny BlockControl entity
- Zachová `text`, `options`, `classes`, `styles` atributy
- Duplikuje i media asociovaná s bloky

#### Extrakce a překlad textů
- Identifikuje přeložitelné texty ze stránky (title, heading, meta_*)
- Extrahuje texty z Block options (layer_attributes)
- Extrahuje texty z BlockControl (text, options)
- Volá DeepLService pro překlad
- Aplikuje přeložené texty zpět na duplikované entity

## Použití

### Základní použití
```ruby
# V Rails konzoli nebo controlleru
service = PageTranslatorService.new(original_page_id, target_locale)
translated_page = service.call

if translated_page
  puts "Překlad úspěšný! ID: #{translated_page.id}"
  puts "Nový slug: #{translated_page.slug}"
else
  puts "Překlad se nezdařil"
end
```

### Příklad s konkrétními daty
```ruby
# Najdi původní stránku
original_page = Page.find_by(locale: "cs", title: "Domovská stránka")

# Přelož do angličtiny
service = PageTranslatorService.new(original_page.id, "en")
english_page = service.call

# Zkontroluj výsledek
puts "Původní: #{original_page.title}"
puts "Přeloženo: #{english_page.title}"
# => "Původní: Domovská stránka"
# => "Přeloženo: Domovská stránka_translated_en"
```

## Bezpečnostní kontroly

Service automaticky kontroluje:
- ✅ Zda cílový jazyk je jiný než původní
- ✅ Zda cílový jazyk je v seznamu dostupných jazyků
- ✅ Zda překlad již neexistuje
- ✅ Validitu původní stránky

## Testování

Spusť testy pro ověření funkčnosti:

```bash
# Test DeepLService
rails test test/services/deep_l_service_test.rb

# Test základní struktury a asociací
rails test test/services/page_translator_demo_test.rb
```

## Rozšíření pro produkci

### 1. Skutečné DeepL API
Nahraď simulaci v `DeepLService` skutečným voláním:

```ruby
# V app/services/deep_l_service.rb
def translate(texts_array, source_locale, target_locale)
  # Implementace skutečného DeepL API volání
  # Viz komentáře v souboru pro příklad
end
```

### 2. Konfigurace přeložitelných atributů
Vytvoř konfigurační soubor pro správu přeložitelných atributů:

```ruby
# config/translatable_attributes.yml
page:
  - title
  - heading
  - meta_title
  - meta_description

block_control:
  text_attributes:
    - text
    - primary_button_text
    - secondary_button_text
    - pre_header
```

### 3. Background jobs
Pro větší stránky použij background job:

```ruby
class PageTranslationJob < ApplicationJob
  def perform(page_id, target_locale)
    PageTranslatorService.new(page_id, target_locale).call
  end
end
```

### 4. UI integrace
Přidej tlačítka pro překlad do admin rozhraní:

```erb
<%= link_to "Přeložit do EN", 
    translate_page_path(@page, locale: "en"), 
    method: :post, 
    class: "btn btn-primary" %>
```

## Struktura souborů

```
app/
├── models/
│   ├── page.rb              # ✅ Rozšířeno o translation asociace
│   ├── block.rb             # ✅ Rozšířeno o translation asociace
│   └── review.rb            # ✅ Nový model pro Website asociace
├── services/
│   ├── page_translator_service.rb  # ✅ Hlavní překladová služba
│   └── deep_l_service.rb           # ✅ Simulace DeepL API
db/
├── migrate/
│   ├── *_add_translation_of_id_to_pages.rb      # ✅ Nová migrace
│   └── *_add_translated_from_block_id_to_blocks.rb  # ✅ Nová migrace
test/
└── services/
    ├── deep_l_service_test.rb           # ✅ Testy DeepL služby
    └── page_translator_demo_test.rb     # ✅ Demo testy struktury
```

## Status implementace

- ✅ **Databázová struktura** - Kompletní
- ✅ **Model asociace** - Kompletní  
- ✅ **Základní služby** - Kompletní
- ✅ **Klonování stránek** - Kompletní
- ✅ **Duplikace bloků** - Kompletní
- ✅ **Extrakce textů** - Kompletní
- ✅ **Simulace překladu** - Kompletní
- ✅ **Základní testy** - Kompletní
- 🔄 **Skutečné DeepL API** - Připraveno k implementaci
- 🔄 **UI integrace** - Připraveno k implementaci
- 🔄 **Background jobs** - Připraveno k implementaci

Implementace je připravena pro produkční použití s možností snadného rozšíření o skutečné DeepL API a UI komponenty.
