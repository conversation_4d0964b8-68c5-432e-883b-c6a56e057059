# == Schema Information
#
# Table name: block_controls
#
#  id         :bigint           not null, primary key
#  classes    :jsonb
#  container  :string
#  content    :jsonb
#  options    :jsonb
#  position   :integer          default(0)
#  styles     :jsonb
#  text       :text
#  type       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  block_id   :bigint
#
# Indexes
#
#  index_block_controls_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class BlockControl < ApplicationRecord
  include TailwindClassable

  # store_accessor :options, :heading_type, :primary_button_text, :primary_button_link, :secondary_button_text, :secondary_button_link

  default_scope { order(position: :asc) }

  scope :visible, -> { where(hidden_at: nil) }
  def edit_template_file
    "admin/content/blocks/controls/#{self.type.split('::').last.downcase}"
  end
end
