# == Schema Information
#
# Table name: pages
#
#  id                :bigint           not null, primary key
#  ancestry          :string
#  ancestry_depth    :integer          default(0)
#  heading           :string
#  is_homepage       :boolean          default(FALSE)
#  link              :string
#  locale            :string           default("cs")
#  meta_description  :string(500)
#  meta_title        :string
#  position          :integer
#  published_at      :datetime
#  slug              :string
#  title             :string
#  type              :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  anchor_block_id   :bigint
#  scope_id          :bigint
#  template_id       :bigint
#  translation_of_id :bigint
#  website_id        :bigint
#
# Indexes
#
#  index_pages_on_ancestry           (ancestry)
#  index_pages_on_anchor_block_id    (anchor_block_id)
#  index_pages_on_scope_id           (scope_id)
#  index_pages_on_template_id        (template_id)
#  index_pages_on_translation_of_id  (translation_of_id)
#  index_pages_on_website_id         (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (anchor_block_id => blocks.id)
#  fk_rails_...  (scope_id => pages.id)
#  fk_rails_...  (template_id => templates.id)
#  fk_rails_...  (translation_of_id => pages.id)
#  fk_rails_...  (website_id => websites.id)
#
class Link < Page
  def self.model_name
    Page.model_name
  end
end
