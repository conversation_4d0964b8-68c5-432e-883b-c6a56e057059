require "test_helper"

class DeepLApiTest < Minitest::Test
  def setup
    @service = DeepLService.new
  end

  def test_real_deepl_translation
    # Test skutečného překladu (pouze pokud je API dostupné)
    texts = ["Dobr<PERSON> den", "<PERSON><PERSON> se máte?"]
    
    begin
      translated = @service.translate(texts, "cs", "en")
      
      assert_equal texts.length, translated.length
      refute_equal texts[0], translated[0], "Text by měl b<PERSON><PERSON>"
      refute_equal texts[1], translated[1], "Text by měl b<PERSON>t <PERSON>"
      
      puts "✅ DeepL API překlad úspěšný:"
      puts "   CS: #{texts.inspect}"
      puts "   EN: #{translated.inspect}"
      
    rescue => e
      puts "⚠️  DeepL API nedostupné nebo chyba: #{e.message}"
      puts "   Testuje se fallback..."
      
      # Test fallback funkčnosti
      translated = @service.translate(texts, "cs", "en")
      assert_equal texts.length, translated.length
      assert_includes translated[0], "_translated_en"
      
      puts "✅ Fallback překlad funguje"
    end
  end

  def test_locale_mapping
    service = @service
    
    # Test mapování jazyků
    assert_equal 'CS', service.send(:map_locale_to_deepl, 'cs')
    assert_equal 'EN', service.send(:map_locale_to_deepl, 'en')
    assert_equal 'DE', service.send(:map_locale_to_deepl, 'de')
    assert_equal 'PL', service.send(:map_locale_to_deepl, 'pl')
    
    puts "✅ Mapování jazyků funguje správně"
  end

  def test_empty_and_nil_handling
    # Test prázdných a nil hodnot
    texts = ["Ahoj", "", nil, "Děkuji"]
    translated = @service.translate(texts, "cs", "en")
    
    assert_equal 4, translated.length
    refute_equal "Ahoj", translated[0] unless translated[0].include?("_translated_")
    assert_equal "", translated[1]
    assert_nil translated[2]
    refute_equal "Děkuji", translated[3] unless translated[3].include?("_translated_")
    
    puts "✅ Zpracování prázdných hodnot funguje"
  end

  def test_html_preservation
    # Test zachování HTML tagů
    texts = ["<strong>Tučný text</strong>", "<em>Kurzíva</em> a <br> nový řádek"]
    
    begin
      translated = @service.translate(texts, "cs", "en")
      
      # Ověř, že HTML tagy jsou zachovány
      assert_includes translated[0], "<strong>"
      assert_includes translated[0], "</strong>"
      assert_includes translated[1], "<em>"
      assert_includes translated[1], "<br>"
      
      puts "✅ HTML tagy jsou zachovány:"
      puts "   Původní: #{texts.inspect}"
      puts "   Přeložené: #{translated.inspect}"
      
    rescue => e
      puts "⚠️  HTML test skipped - API nedostupné: #{e.message}"
    end
  end
end
