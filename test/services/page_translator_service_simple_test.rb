require "test_helper"

class PageTranslatorServiceSimpleTest < Minitest::Test
  def setup
    # Vytvoř testovací data bez fixtures
    @account = Account.create!(name: "Test Account")
    @website = Website.create!(
      account: @account,
      name: "Test Website",
      email: "<EMAIL>",
      phone: "+************",
      locale: "cs",
      available_locales: ["cs", "en"]
    )

    # Nastav tenant pro Page model
    ActsAsTenant.current_tenant = @website

    @original_page = Page.create!(
      website: @website,
      title: "Původn<PERSON> stránka",
      heading: "Původní nadpis",
      meta_title: "Původní meta title",
      meta_description: "Původní meta popis",
      locale: "cs",
      type: "Content"
    )
  end

  def teardown
    # Vyčisti testovací data
    ActsAsTenant.without_tenant do
      Page.destroy_all
      Website.destroy_all
      Account.destroy_all
    end
  end

  def test_should_translate_page_successfully
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert translated_page, "<PERSON><PERSON><PERSON>lad by m<PERSON><PERSON> <PERSON><PERSON><PERSON>"
    assert_equal "en", translated_page.locale
    assert_equal @original_page.id, translated_page.translation_of_id
    assert_equal @original_page.website_id, translated_page.website_id
    
    # Ověř, že texty byly "přeloženy" (s suffixem)
    assert_includes translated_page.title, "_translated_en"
    assert_includes translated_page.heading, "_translated_en"
    assert_includes translated_page.meta_title, "_translated_en"
    assert_includes translated_page.meta_description, "_translated_en"
  end

  def test_should_not_translate_to_same_locale
    service = PageTranslatorService.new(@original_page.id, "cs")
    translated_page = service.call
    
    assert_nil translated_page, "Překlad do stejného jazyka by neměl být povolen"
  end

  def test_should_not_translate_if_translation_already_exists
    # Vytvoř existující překlad
    existing_translation = Page.create!(
      website: @website,
      locale: "en",
      title: "Existing translation",
      translation_of_id: @original_page.id,
      type: "Content"
    )
    
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert_nil translated_page, "Překlad by neměl být vytvořen, pokud již existuje"
  end

  def test_should_handle_invalid_locale
    service = PageTranslatorService.new(@original_page.id, "invalid")
    translated_page = service.call
    
    assert_nil translated_page, "Překlad do nevalidního jazyka by neměl být povolen"
  end

  def test_should_generate_unique_slug_for_translation
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert translated_page.slug, "Přeložená stránka by měla mít slug"
    refute_equal @original_page.slug, translated_page.slug, "Slug by měl být jiný než u originální stránky"
  end

  def test_should_duplicate_blocks_and_controls
    # Vytvoř testovací blok s ovládacím prvkem
    block = Block.create!(
      page: @original_page,
      name: "test_block",
      position: 1,
      options: {
        "content_layer_attributes" => {
          "text" => "Testovací text v bloku"
        }
      }
    )
    
    control = BlockControl.create!(
      block_id: block.id,
      type: "BlockControls::Heading",
      text: "Testovací nadpis",
      position: 1,
      options: {
        "heading_type" => "h2",
        "pre_header" => "Přednadpis"
      }
    )
    
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert_equal @original_page.blocks.count, translated_page.blocks.count
    
    translated_block = translated_page.blocks.first
    assert_equal block.name, translated_block.name
    assert_equal block.id, translated_block.translated_from_block_id
    
    # Ověř duplikaci ovládacích prvků
    assert_equal block.controls.count, translated_block.controls.count
    
    translated_control = translated_block.controls.first
    assert_includes translated_control.text, "_translated_en"
    assert_includes translated_control.options["pre_header"], "_translated_en"
  end
end
