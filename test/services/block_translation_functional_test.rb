require "test_helper"

class BlockTranslationFunctionalTest < Minitest::Test
  def setup
    # Vytvoř testovací data v <PERSON><PERSON><PERSON><PERSON>
    @account = Account.create!(name: "Test Account")
    @website = Website.create!(
      account: @account,
      name: "Test Website",
      email: "<EMAIL>",
      phone: "+************",
      locale: "cs",
      available_locales: ["cs", "en"]
    )
    
    # Nastav tenant
    ActsAsTenant.current_tenant = @website
    
    @page = Page.create!(
      website: @website,
      title: "Test stránka",
      locale: "cs",
      type: "Content"
    )
    
    @block = Block.create!(
      name: "test_block",
      options: {}
    )
    
    # Vytvoř page-block vztah
    @page_block = PageBlock.create!(
      page: @page,
      block: @block,
      position: 1
    )
    
    @control = BlockControl.create!(
      block: @block,
      type: "BlockControl",
      text: "Původní český text",
      position: 1
    )

    # Nastav text_translations po vytvoření
    @control.update!(text_translations: { "cs" => "<PERSON><PERSON><PERSON><PERSON><PERSON> text" })
  end

  def teardown
    ActsAsTenant.without_tenant do
      PageBlock.destroy_all
      BlockControl.destroy_all
      Block.destroy_all
      Page.destroy_all
      Website.destroy_all
      Account.destroy_all
    end
  end

  def test_page_block_many_to_many_relationship
    # Test, že page-block vztah funguje
    assert_equal 1, @page.blocks.count
    assert_equal @block, @page.blocks.first
    
    assert_equal 1, @block.pages.count
    assert_equal @page, @block.pages.first
    
    puts "✅ Many-to-many vztah Page ↔ Block funguje"
  end

  def test_block_can_be_shared_between_pages
    # Vytvoř druhou stránku
    page2 = Page.create!(
      website: @website,
      title: "Druhá stránka",
      locale: "en",
      type: "Content"
    )
    
    # Přidej stejný blok na druhou stránku
    PageBlock.create!(
      page: page2,
      block: @block,
      position: 1
    )
    
    # Zkontroluj, že blok je sdílený
    assert_equal 2, @block.pages.count
    assert_includes @block.pages, @page
    assert_includes @block.pages, page2
    
    puts "✅ Blok je sdílený mezi #{@block.pages.count} stránkami"
  end

  def test_block_control_text_translations
    # Test, že text_translations funguje
    assert_equal "Původní český text", @control.text_translations["cs"]
    
    # Přidej anglický překlad
    @control.text_translations["en"] = "Original English text"
    @control.save!
    
    @control.reload
    assert_equal "Original English text", @control.text_translations["en"]
    assert_equal "Původní český text", @control.text_translations["cs"]
    
    puts "✅ text_translations JSONB sloupec funguje správně"
  end

  def test_mobility_locale_switching
    # Nastav překlady
    @control.update!(
      text_translations: {
        "cs" => "Český text",
        "en" => "English text"
      }
    )
    
    # Test přepínání locale
    Mobility.with_locale("cs") do
      assert_equal "Český text", @control.text
    end
    
    Mobility.with_locale("en") do
      assert_equal "English text", @control.text
    end
    
    puts "✅ Mobility locale přepínání funguje"
  end

  def test_block_translation_service_basic
    # Test základní funkcionalita služby
    service = BlockTranslationService.new(@block, "en", "cs")
    
    # Test validace
    assert service.send(:valid_translation_request?)
    
    puts "✅ BlockTranslationService základní validace funguje"
  end

  def test_block_translation_service_with_real_translation
    # Nastav český text
    @control.update!(
      text_translations: { "cs" => "Ahoj světe" }
    )
    
    # Spusť překlad
    service = BlockTranslationService.new(@block, "en", "cs")
    result = service.call
    
    assert result, "Překlad by měl být úspěšný"
    
    # Zkontroluj, že anglický překlad byl vytvořen
    @control.reload
    
    Mobility.with_locale("en") do
      english_text = @control.text
      assert english_text.present?, "Anglický text by měl existovat"
      refute_equal "Ahoj světe", english_text, "Text by měl být přeložen"
      
      puts "✅ Překlad úspěšný: 'Ahoj světe' -> '#{english_text}'"
    end
    
    # Zkontroluj, že český text zůstal zachován
    Mobility.with_locale("cs") do
      assert_equal "Ahoj světe", @control.text
    end
  end

  def test_migration_data_integrity
    # Test, že migrace zachovala data
    legacy_blocks = Block.where.not(page_id: nil)
    page_blocks_count = PageBlock.count
    
    puts "✅ Migrace: #{legacy_blocks.count} legacy bloků, #{page_blocks_count} page-block vztahů"
    
    # Pokud existují legacy bloky, měly by mít odpovídající page_blocks
    legacy_blocks.each do |block|
      page_block = PageBlock.find_by(block: block, page_id: block.page_id)
      assert page_block, "Block #{block.id} by měl mít odpovídající PageBlock"
    end
  end
end
