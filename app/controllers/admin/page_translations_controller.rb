module Admin
  class PageTranslationsController < Content::ContentController
    before_action :set_page
    before_action :set_available_locales
    before_action :set_breadcrumbs

  def index
    @translations = @page.translations.includes(:website)
    @available_target_locales = @available_locales - [@page.locale]
  end

  def create
    @target_locale = params[:target_locale]
    
    unless @available_locales.include?(@target_locale)
      redirect_to admin_page_page_translations_path(@page),
                  alert: "Neplatný cílový jazyk: #{@target_locale}"
      return
    end

    # Zkontroluj, zda překlad již neexistuje
    existing_translation = @page.translations.find_by(locale: @target_locale)
    if existing_translation
      redirect_to admin_page_page_translations_path(@page),
                  alert: "Překlad do jazyka #{locale_name(@target_locale)} již existuje"
      return
    end

    service = PageTranslatorService.new(@page.id, @target_locale)
    translated_page = service.call

    if translated_page
      redirect_to admin_page_page_translations_path(@page),
                  notice: "<PERSON><PERSON><PERSON><PERSON> byla úspěšně přeložena do jazyka #{locale_name(@target_locale)}"
    else
      redirect_to admin_page_page_translations_path(@page),
                  alert: "Chyba při překladu stránky"
    end
  end

  def destroy
    @translation = @page.translations.find(params[:id])

    if @translation.destroy
      redirect_to admin_page_page_translations_path(@page),
                  notice: "Překlad byl úspěšně smazán"
    else
      redirect_to admin_page_page_translations_path(@page),
                  alert: "Chyba při mazání překladu"
    end
  end

  private

  def set_page
    @page = Page.friendly.find(params[:page_id])
  end

  def set_available_locales
    @available_locales = @page.website.available_locales || Website::AVAILABLE_LOCALES
  end

  def locale_name(locale)
    Website::LOCALE_NAMES[locale.to_sym] || locale.upcase
  end

  def set_breadcrumbs
    add_breadcrumb "Stránky", admin_pages_path
    add_breadcrumb @page.title, edit_admin_page_path(@page)
    add_breadcrumb "Překlady"
  end
  end
end
