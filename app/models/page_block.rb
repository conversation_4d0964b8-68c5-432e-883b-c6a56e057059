# == Schema Information
#
# Table name: page_blocks
#
#  id         :bigint           not null, primary key
#  position   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  block_id   :bigint           not null
#  page_id    :bigint           not null
#
# Indexes
#
#  index_page_blocks_on_block_id              (block_id)
#  index_page_blocks_on_page_id               (page_id)
#  index_page_blocks_on_page_id_and_block_id  (page_id,block_id) UNIQUE
#  index_page_blocks_on_page_id_and_position  (page_id,position)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#  fk_rails_...  (page_id => pages.id)
#
class PageBlock < ApplicationRecord
  belongs_to :page
  belongs_to :block
  
  validates :position, presence: true, uniqueness: { scope: :page_id }
  validates :page_id, uniqueness: { scope: :block_id }
  
  # Positioning within page
  positioned on: :page_id
  
  scope :ordered, -> { order(:position) }
end
