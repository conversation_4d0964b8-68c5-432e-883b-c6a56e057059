class MigrateExistingPageBlocks < ActiveRecord::Migration[8.0]
  def up
    # Migrace existujících page-block vztahů do nové many-to-many tabulky
    execute <<-SQL
      INSERT INTO page_blocks (page_id, block_id, position, created_at, updated_at)
      SELECT page_id, id, position, created_at, updated_at
      FROM blocks 
      WHERE page_id IS NOT NULL
      ORDER BY page_id, position
    SQL
    
    # Migrace existujících textů do text_translations pro default locale
    Block.joins(:legacy_page).includes(:controls).find_each do |block|
      default_locale = block.legacy_page.locale || 'cs'
      
      block.controls.each do |control|
        next unless control.text.present?
        
        # Inicializuj text_translations s existujícím textem
        control.update_column(
          :text_translations, 
          { default_locale => control.text }
        )
      end
    end
    
    puts "✅ Migrace dokončena: #{PageBlock.count} page-block vztah<PERSON> vytvo<PERSON>eno"
  end

  def down
    # Rollback - smaž page_blocks a vyčisti text_translations
    PageBlock.delete_all
    
    execute <<-SQL
      UPDATE block_controls 
      SET text_translations = '{}'::jsonb
    SQL
    
    puts "⏪ Rollback dokončen"
  end
end
