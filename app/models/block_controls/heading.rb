# == Schema Information
#
# Table name: block_controls
#
#  id         :bigint           not null, primary key
#  classes    :jsonb
#  container  :string
#  content    :jsonb
#  options    :jsonb
#  position   :integer          default(0)
#  styles     :jsonb
#  text       :text
#  type       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  block_id   :bigint
#
# Indexes
#
#  index_block_controls_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class BlockControls::Heading < BlockControl
  store_accessor :options, :heading_type, :pre_header

  validates :heading_type, presence: true
  validates :heading_type, inclusion: { in: %w[h1 h2 h3] }

  def self.permitted_attributes
    %i[id text heading_type pre_header]
  end

  def self.sti_name
    'Heading'
  end
end
