require "test_helper"

class BlockTranslationSimpleTest < Minitest::Test
  def test_mobility_integration_works
    # Test základní Mobility funkcionalitu
    control = BlockControl.new(
      type: "BlockControl",
      text: "Původní text",
      options: {},
      position: 1
    )
    
    # Test, že Mobility metody existují
    assert control.respond_to?(:text_translations)
    assert control.respond_to?(:text)
    
    puts "✅ Mobility integrace funguje"
  end

  def test_page_block_association
    # Test nové many-to-many asociace
    page_block = PageBlock.new(position: 1)
    
    assert page_block.respond_to?(:page)
    assert page_block.respond_to?(:block)
    
    puts "✅ PageBlock asociace fungují"
  end

  def test_block_translation_service_exists
    # Test, že služba existuje a má správné metody
    service_class = BlockTranslationService
    
    assert service_class.respond_to?(:new)
    
    instance_methods = service_class.instance_methods(false)
    assert_includes instance_methods, :call
    assert_includes instance_methods, :translate_block_controls
    
    puts "✅ BlockTranslationService má správnou strukturu"
    puts "   Instance metody: #{instance_methods.grep(/translate|call/).sort}"
  end

  def test_deepl_service_still_works
    # Test, že DeepL služba stále funguje
    service = DeepLService.new
    texts = ["Ahoj", "Jak se máš?"]
    
    begin
      translated = service.translate(texts, "cs", "en")
      
      assert_equal texts.length, translated.length
      refute_equal texts[0], translated[0] unless translated[0].include?("_translated_")
      
      puts "✅ DeepL služba funguje"
      puts "   Překlad: #{texts.first} -> #{translated.first}"
    rescue => e
      puts "⚠️ DeepL API nedostupné, testuje se fallback: #{e.message}"
      
      # Test fallback
      translated = service.translate(texts, "cs", "en")
      assert_includes translated[0], "_translated_en"
      puts "✅ Fallback funguje"
    end
  end

  def test_text_translations_column_exists
    # Test, že nový sloupec existuje
    assert BlockControl.column_names.include?("text_translations")
    
    # Test, že je to JSONB
    column = BlockControl.columns_hash["text_translations"]
    assert_equal "jsonb", column.sql_type
    
    puts "✅ text_translations sloupec existuje a je JSONB"
  end

  def test_page_blocks_table_exists
    # Test, že nová tabulka existuje
    assert ActiveRecord::Base.connection.table_exists?("page_blocks")
    
    # Test sloupců
    columns = ActiveRecord::Base.connection.columns("page_blocks").map(&:name)
    assert_includes columns, "page_id"
    assert_includes columns, "block_id"
    assert_includes columns, "position"
    
    puts "✅ page_blocks tabulka existuje se správnými sloupci"
    puts "   Sloupce: #{columns.sort}"
  end

  def test_migration_created_page_blocks
    # Test, že migrace vytvořila page_blocks záznamy
    count = PageBlock.count
    
    puts "✅ Migrace vytvořila #{count} page-block vztahů"
    
    if count > 0
      sample = PageBlock.first
      puts "   Ukázka: Page #{sample.page_id} -> Block #{sample.block_id} (pozice #{sample.position})"
    end
  end
end
