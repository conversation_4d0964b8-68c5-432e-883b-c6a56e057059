# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_03_122250) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "hstore"
  enable_extension "pg_catalog.plpgsql"

  create_table "account_users", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_account_users_on_account_id"
    t.index ["user_id"], name: "index_account_users_on_user_id"
  end

  create_table "accounts", force: :cascade do |t|
    t.string "name"
    t.string "billing_details"
    t.string "billing_email"
    t.string "domain"
    t.string "subdomain"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "block_controls", force: :cascade do |t|
    t.string "type"
    t.text "text"
    t.jsonb "styles"
    t.jsonb "options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", default: 0
    t.jsonb "classes", default: {}
    t.jsonb "content", default: {}
    t.bigint "block_id"
    t.string "container"
    t.index ["block_id"], name: "index_block_controls_on_block_id"
  end

  create_table "blocks", force: :cascade do |t|
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "hidden_at"
    t.jsonb "options", default: {}
    t.jsonb "media_options", default: {}
    t.bigint "page_id"
    t.string "name"
    t.jsonb "pricing_options", default: {}
    t.bigint "pricing_id"
    t.bigint "media_collection_id"
    t.bigint "media_type_id"
    t.bigint "translated_from_block_id"
    t.index ["media_collection_id"], name: "index_blocks_on_media_collection_id"
    t.index ["media_type_id"], name: "index_blocks_on_media_type_id"
    t.index ["pricing_id"], name: "index_blocks_on_pricing_id"
    t.index ["translated_from_block_id"], name: "index_blocks_on_translated_from_block_id"
  end

  create_table "forms", force: :cascade do |t|
    t.string "name"
    t.integer "form_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.boolean "enabled", default: true
    t.jsonb "options", default: {}
    t.index ["website_id"], name: "index_forms_on_website_id"
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string "slug", null: false
    t.integer "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.index ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true
    t.index ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type"
    t.index ["sluggable_type", "sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_type_and_sluggable_id"
  end

  create_table "html_tags", force: :cascade do |t|
    t.string "name"
    t.text "content"
    t.integer "position"
    t.boolean "active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "page_id", null: false
  end

  create_table "icons", force: :cascade do |t|
    t.text "svg_html"
    t.text "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "images", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "image_url"
    t.index ["website_id"], name: "index_images_on_website_id"
  end

  create_table "inbox_messages", force: :cascade do |t|
    t.bigint "form_id", null: false
    t.text "message"
    t.string "email"
    t.string "phone"
    t.string "name"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.index ["form_id"], name: "index_inbox_messages_on_form_id"
    t.index ["website_id"], name: "index_inbox_messages_on_website_id"
  end

  create_table "media", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "title"
    t.string "unique_id"
    t.jsonb "data"
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "origin"
    t.datetime "removed_at"
    t.text "content", default: "{}"
    t.bigint "image_id"
    t.bigint "icon_id"
    t.bigint "media_collection_id"
    t.integer "position", default: 1, null: false
    t.bigint "media_type_id"
    t.bigint "block_id"
    t.text "text"
    t.index ["block_id"], name: "index_media_on_block_id"
    t.index ["icon_id"], name: "index_media_on_icon_id"
    t.index ["image_id"], name: "index_media_on_image_id"
    t.index ["media_collection_id"], name: "index_media_on_media_collection_id"
    t.index ["media_type_id"], name: "index_media_on_media_type_id"
    t.index ["website_id"], name: "index_media_on_website_id"
  end

  create_table "media_collections", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "collection_type"
    t.string "source"
    t.index ["website_id"], name: "index_media_collections_on_website_id"
  end

  create_table "media_fields", force: :cascade do |t|
    t.bigint "media_type_id", null: false
    t.string "field_key"
    t.string "field_type"
    t.integer "position"
    t.boolean "required"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["media_type_id"], name: "index_media_fields_on_media_type_id"
  end

  create_table "media_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "has_groups", default: false
    t.string "slug"
  end

  create_table "opening_hours", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.integer "day", default: 0
    t.datetime "date"
    t.time "opening_hour"
    t.time "closing_hour"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id", "date"], name: "index_opening_hours_on_website_id_and_date", unique: true
    t.index ["website_id"], name: "index_opening_hours_on_website_id"
  end

  create_table "pages", force: :cascade do |t|
    t.string "title"
    t.string "slug"
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ancestry", collation: "C"
    t.integer "ancestry_depth", default: 0
    t.string "meta_title"
    t.string "meta_description", limit: 500
    t.string "locale", default: "cs"
    t.integer "position"
    t.string "type"
    t.bigint "website_id"
    t.string "link"
    t.boolean "is_homepage", default: false
    t.string "heading"
    t.bigint "anchor_block_id"
    t.bigint "template_id"
    t.bigint "scope_id"
    t.bigint "translation_of_id"
    t.index ["ancestry"], name: "index_pages_on_ancestry"
    t.index ["anchor_block_id"], name: "index_pages_on_anchor_block_id"
    t.index ["scope_id"], name: "index_pages_on_scope_id"
    t.index ["template_id"], name: "index_pages_on_template_id"
    t.index ["translation_of_id"], name: "index_pages_on_translation_of_id"
    t.index ["website_id"], name: "index_pages_on_website_id"
  end

  create_table "pricing", force: :cascade do |t|
    t.string "pricing_type"
    t.datetime "valid_from"
    t.datetime "valid_to"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.integer "position"
    t.string "locale"
    t.string "name"
    t.index ["website_id"], name: "index_pricing_on_website_id"
  end

  create_table "pricing_items", force: :cascade do |t|
    t.bigint "pricing_section_id", null: false
    t.decimal "price", precision: 10, scale: 2
    t.decimal "price_eur", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.hstore "name", default: {}, null: false
    t.hstore "description", default: {}, null: false
    t.integer "duration"
    t.index ["pricing_section_id"], name: "index_pricing_items_on_pricing_section_id"
  end

  create_table "pricing_sections", force: :cascade do |t|
    t.bigint "pricing_id", null: false
    t.string "name"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "valid_date"
    t.index ["pricing_id"], name: "index_pricing_sections_on_pricing_id"
  end

  create_table "reservations", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "name"
    t.string "email"
    t.string "phone"
    t.date "date"
    t.datetime "time"
    t.integer "guests"
    t.text "note"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_reservations_on_website_id"
  end

  create_table "reviews", force: :cascade do |t|
    t.string "name"
    t.text "content"
    t.text "origin_id"
    t.string "origin_name"
    t.integer "rating"
    t.datetime "published_at"
    t.text "origin_url"
    t.datetime "approved_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.datetime "removed_at"
    t.index ["website_id"], name: "index_reviews_on_website_id"
  end

  create_table "services", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "type"
    t.jsonb "options"
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_services_on_website_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "templates", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "name"
    t.string "color_theme"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_templates_on_website_id"
  end

  create_table "themes", force: :cascade do |t|
    t.jsonb "options"
    t.bigint "website_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_themes_on_website_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "password_digest"
    t.string "first_name"
    t.string "last_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "remember_token"
  end

  create_table "webhooks", force: :cascade do |t|
    t.string "event_type"
    t.jsonb "payload"
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "websites", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.string "name"
    t.string "address"
    t.string "city"
    t.string "postal_code"
    t.string "country"
    t.string "phone"
    t.string "email"
    t.string "domain"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "locale", default: "cs"
    t.jsonb "available_locales", default: []
    t.jsonb "data", default: {}
    t.string "map_url"
    t.jsonb "social_networks", default: {}
    t.jsonb "theme", default: {}
    t.index ["account_id"], name: "index_websites_on_account_id"
  end

  add_foreign_key "account_users", "accounts"
  add_foreign_key "account_users", "users"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "block_controls", "blocks"
  add_foreign_key "blocks", "blocks", column: "translated_from_block_id"
  add_foreign_key "blocks", "media_collections"
  add_foreign_key "blocks", "media_collections"
  add_foreign_key "blocks", "media_types"
  add_foreign_key "blocks", "pages"
  add_foreign_key "blocks", "pricing"
  add_foreign_key "forms", "websites"
  add_foreign_key "html_tags", "pages"
  add_foreign_key "images", "websites"
  add_foreign_key "inbox_messages", "forms"
  add_foreign_key "inbox_messages", "websites"
  add_foreign_key "media", "blocks"
  add_foreign_key "media", "icons"
  add_foreign_key "media", "images"
  add_foreign_key "media", "media_collections"
  add_foreign_key "media", "media_collections"
  add_foreign_key "media", "media_types"
  add_foreign_key "media", "websites"
  add_foreign_key "media_collections", "websites"
  add_foreign_key "media_fields", "media_types"
  add_foreign_key "opening_hours", "websites"
  add_foreign_key "pages", "blocks", column: "anchor_block_id"
  add_foreign_key "pages", "pages", column: "scope_id"
  add_foreign_key "pages", "pages", column: "translation_of_id"
  add_foreign_key "pages", "templates"
  add_foreign_key "pages", "websites"
  add_foreign_key "pricing", "websites"
  add_foreign_key "pricing_items", "pricing_sections"
  add_foreign_key "pricing_sections", "pricing"
  add_foreign_key "reservations", "websites"
  add_foreign_key "reviews", "websites"
  add_foreign_key "services", "websites"
  add_foreign_key "sessions", "users"
  add_foreign_key "templates", "websites"
  add_foreign_key "themes", "websites"
  add_foreign_key "websites", "accounts"
end
