class Admin::PageTranslationsController < ApplicationController
  before_action :set_page
  before_action :set_available_locales

  def index
    @translations = @page.translations.includes(:website)
    @available_target_locales = @available_locales - [@page.locale]
  end

  def create
    @target_locale = params[:target_locale]
    
    unless @available_locales.include?(@target_locale)
      redirect_to admin_page_translations_path(@page), 
                  alert: "Neplatný cílový jazyk: #{@target_locale}"
      return
    end

    # Zkontroluj, zda překlad již neexistuje
    existing_translation = @page.translations.find_by(locale: @target_locale)
    if existing_translation
      redirect_to admin_page_translations_path(@page), 
                  alert: "Překlad do jazyka #{locale_name(@target_locale)} již existuje"
      return
    end

    service = PageTranslatorService.new(@page.id, @target_locale)
    translated_page = service.call

    if translated_page
      redirect_to admin_page_translations_path(@page), 
                  notice: "<PERSON><PERSON>án<PERSON> byla ú<PERSON><PERSON>n<PERSON> přeložena do jazyka #{locale_name(@target_locale)}"
    else
      redirect_to admin_page_translations_path(@page), 
                  alert: "Chyba při překladu stránky"
    end
  end

  def destroy
    @translation = @page.translations.find(params[:id])
    
    if @translation.destroy
      redirect_to admin_page_translations_path(@page), 
                  notice: "Překlad byl úspěšně smazán"
    else
      redirect_to admin_page_translations_path(@page), 
                  alert: "Chyba při mazání překladu"
    end
  end

  private

  def set_page
    @page = Page.find(params[:page_id])
  end

  def set_available_locales
    @available_locales = @page.website.available_locales || Website::AVAILABLE_LOCALES
  end

  def locale_name(locale)
    Website::LOCALE_NAMES[locale.to_sym] || locale.upcase
  end
end
