# == Schema Information
#
# Table name: block_controls
#
#  id                :bigint           not null, primary key
#  classes           :jsonb
#  container         :string
#  content           :jsonb
#  options           :jsonb
#  position          :integer          default(0)
#  styles            :jsonb
#  text              :text
#  text_translations :jsonb
#  type              :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  block_id          :bigint
#
# Indexes
#
#  index_block_controls_on_block_id           (block_id)
#  index_block_controls_on_text_translations  (text_translations) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class BlockControls::Image < BlockControl
  store_accessor :options, :image_url

  has_one_attached :image do |attachable|
    attachable.variant :mini_preview, resize_and_pad: [ 80, 40 ]
    attachable.variant :preview, resize_to_fit: [ 320, 320 ], saver: { quality: 100 }
  end

  def self.permitted_attributes
    %i[id image]
  end

  def component
    BlockControls::ImageControl.new(self)
  end
end
