# == Schema Information
#
# Table name: blocks
#
#  id                       :bigint           not null, primary key
#  hidden_at                :datetime
#  media_options            :jsonb
#  name                     :string
#  options                  :jsonb
#  position                 :integer
#  pricing_options          :jsonb
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  media_collection_id      :bigint
#  media_type_id            :bigint
#  page_id                  :bigint
#  pricing_id               :bigint
#  translated_from_block_id :bigint
#
# Indexes
#
#  index_blocks_on_media_collection_id       (media_collection_id)
#  index_blocks_on_media_type_id             (media_type_id)
#  index_blocks_on_pricing_id                (pricing_id)
#  index_blocks_on_translated_from_block_id  (translated_from_block_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_type_id => media_types.id)
#  fk_rails_...  (page_id => pages.id)
#  fk_rails_...  (pricing_id => pricing.id)
#  fk_rails_...  (translated_from_block_id => blocks.id)
#
class Block < ApplicationRecord
  include ActionView::Helpers::SanitizeHelper
  include TailwindClassable

  LAYER_CONFIGS = {
    outer_container_layer: { class_name: "Block::OuterContainerLayer" },
    inner_container_layer: { class_name: "Block::InnerContainerLayer" },
    content_layer:         { class_name: "Block::ContentLayer" },
  }.freeze

  # Associations
  # Nový many-to-many vztah se stránkami
  has_many :page_blocks, dependent: :destroy
  has_many :pages, through: :page_blocks

  # Zachováváme starý vztah pro zpětnou kompatibilitu (deprecated)
  belongs_to :legacy_page, class_name: 'Page', foreign_key: 'page_id', optional: true

  belongs_to :pricing, optional: true
  belongs_to :media_collection, optional: true
  belongs_to :media_type, optional: true

  has_many :controls, -> { order(position: :asc) }, class_name: "BlockControl", dependent: :destroy
  has_many :media_in_collection, through: :media_collection, class_name: "Media", source: :media
  has_many :media, -> { order(position: :asc) }, dependent: :destroy, class_name: "Media"

  # Translation associations (deprecated - nahrazeno Mobility)
  belongs_to :original_block, class_name: "Block", foreign_key: "translated_from_block_id", optional: true
  has_many :translated_blocks, class_name: "Block", foreign_key: "translated_from_block_id", dependent: :nullify

  # Attachments
  has_one_attached :background_image do |attachable|
    attachable.variant :thumb, resize_to_fill: [120, 60]
    attachable.variant :preview, resize_to_limit: [ 1024, 768 ], saver: { quality: 100 }
  end

  has_one_attached :background_image_mobile

  # Scopes
  default_scope { order(position: :asc) }
  scope :visible, -> { where(hidden_at: nil) }

  # Store Accessor
  store_accessor :media_options, :inline_items_count, :posts_limit, :gap, :layout, :resize_image_options, :position, prefix: :media
  store_accessor :pricing_options, :pricing_type

  # Positioning
  positioned on: %i[page_id]

  # Nested Attributes
  accepts_nested_attributes_for :controls, allow_destroy: true
  before_save :serialize_all_layers_to_options

  LAYER_CONFIGS.each do |layer_key, config|
    layer_class_str = config[:class_name]
    # Klíč, pod kterým budou atributy této vrstvy uloženy v 'options' JSONu
    attributes_storage_key = "#{layer_key}_attributes" # např. "outer_container_layer_attributes"

    # Getter (např. block.outer_container_layer)
    define_method(layer_key) do
      # Memoizace instance PORO vrstvy
      instance_variable_get("@#{layer_key}") || instance_variable_set("@#{layer_key}",
                                                                      begin
                                                                        layer_class = layer_class_str.safe_constantize
                                                                        unless layer_class
                                                                          Rails.logger.error "Chyba: Třída vrstvy '#{layer_class_str}' nenalezena pro klíč '#{layer_key}'."
                                                                          next nil # Nebo vrátit prázdnou instanci, pokud je to vhodnější
                                                                        end

                                                                        current_options_hash = self.options || {} # Zajistí, že options je hash
                                                                        # Získáme data pro tuto konkrétní vrstvu z hlavního 'options' hashe
                                                                        layer_data_from_options = current_options_hash.fetch(attributes_storage_key, {})

                                                                        # Předpokládáme, že PORO vrstvy mohou přijímat parent_block_id
                                                                        # a že mají definované pouze své relevantní atributy.
                                                                        # Filtrování na známé atributy je dobrá praxe.
                                                                        known_layer_attribute_names = layer_class.attribute_names.map(&:to_sym)
                                                                        attributes_for_poro = layer_data_from_options.deep_symbolize_keys.slice(*known_layer_attribute_names)

                                                                        # Přidání parent_block_id, pokud je PORO navrženo, aby ho přijalo a použilo (např. pro dom_id)
                                                                        # Toto by se mělo dít, pokud parent_block_id je attr_accessor a ne definovaný 'attribute'
                                                                        # nebo pokud je explicitně součástí `initialize`.
                                                                        # Pro jednoduchost: pokud PORO má attr_accessor :parent_block_id a nastavuje ho v initialize
                                                                        # nebo pokud je parent_block_id jedním z `attribute :parent_block_id, :integer`
                                                                        # pak merge je ok. Pokud je to jen attr_accessor, je lepší ho nastavit po super() v PORO init.
                                                                        # Zde pro jednoduchost předpokládáme, že PORO init to zvládne (viz předchozí odpovědi)
                                                                        final_attributes_for_poro = attributes_for_poro

                                                                        layer_class.new(final_attributes_for_poro)
                                                                      rescue => e
                                                                        Rails.logger.error "Chyba při inicializaci vrstvy #{layer_key} pro blok #{self.id || 'nový'}: #{e.message}\n#{e.backtrace.first(5).join("\n")}"
                                                                        layer_class.new(parent_block_id: self.id) if layer_class # Záložní instance jen s ID
                                                                      end
      )
    end

    def serialize_all_layers_to_options
      # Začneme s aktuálním stavem 'options' tak, jak je v paměti (což by mělo být to, co se načetlo z DB,
      # pokud nedošlo k přímému přepsání celého self.options).
      # Je bezpečnější začít s tím, co bylo naposledy v DB, pokud je záznam perzistovaný,
      # abychom měli jistotu, že nepřijdeme o data jiných vrstev.

      # Hluboká kopie, abychom nemodifikovali původní hash, pokud bychom ho ještě potřebovali.
      # options_in_database vrací hodnotu atributu tak, jak byla naposledy načtena z DB (pro Rails 6.1+).
      # Pro starší verze nebo pro větší jistotu můžeme použít self.options, ale musíme si být jisti,
      # že nebylo přepsáno jen částečnými daty.

      # Přístup, který by měl být robustní:
      # 1. Načti aktuální options z databáze (pokud objekt existuje).
      # 2. Pro každou definovanou vrstvu:
      #    a. Pokud byla její PORO instance v tomto requestu vytvořena/změněna, vezmi její aktuální atributy.
      #    b. Jinak ponech data této vrstvy z načteného options (z bodu 1).

      # Načteme aktuální uložený stav options, pokud existuje a je to hash
      current_persisted_options = if persisted? && options_in_database.is_a?(Hash)
                                    options_in_database.deep_dup
                                  elsif self.options.is_a?(Hash) # Pro nové záznamy nebo pokud options_in_database není dostupné
                                    self.options.deep_dup
                                  else
                                    {}
                                  end

      final_options_to_save = current_persisted_options

      LAYER_CONFIGS.each_key do |layer_key|
        # Získáme instanci PORO vrstvy, pokud byla v tomto requestu vytvořena/použita
        # (např. přes getter block.media_layer nebo setter block.media_layer_attributes=)
        layer_poro_instance = instance_variable_get("@#{layer_key}")
        attributes_storage_key = "#{layer_key}_attributes" # např. "media_layer_attributes"

        if layer_poro_instance
          # Pokud byla PORO instance pro tuto vrstvu vytvořena/změněna,
          # použijeme její aktuální atributy.
          final_options_to_save[attributes_storage_key] = layer_poro_instance.attributes.compact
        elsif !final_options_to_save.key?(attributes_storage_key)
          # Pokud PORO nebylo inicializováno A klíč pro tuto vrstvu v options ještě neexistuje
          # (např. přidali jste novou vrstvu do LAYER_CONFIGS a aktualizujete starý blok),
          # vytvoříme pro ni prázdný hash, aby struktura options byla kompletní.
          final_options_to_save[attributes_storage_key] = {}
        end
        # Pokud layer_poro_instance je nil (vrstva nebyla v tomto requestu "dotčena"),
        # A final_options_to_save[attributes_storage_key] již obsahuje data z DB (načtená výše),
        # tak tato data zůstanou zachována.
      end

      self.options = final_options_to_save
    end

    # Setter (např. block.outer_container_layer_attributes = { ... })
    define_method("#{layer_key}_attributes=") do |new_attributes_hash|
      layer_object = send(layer_key) # Zavolá getter, který inicializuje PORO pokud je potřeba

      # Pokud getter vrátil nil (např. třída nebyla nalezena), pokusíme se vytvořit novou instanci
      if layer_object.nil? && (layer_class = layer_class_str.safe_constantize)
        # Vytvoříme novou instanci s parent_block_id, pokud je to relevantní
        layer_object = layer_class.new(parent_block_id: self.id)
        instance_variable_set("@#{layer_key}", layer_object)
      end

      layer_object&.assign_attributes(new_attributes_hash)
      # Důležité: označí hlavní atribut `options` jako změněný,
      # protože jeho obsah (vnořený hash pro tuto vrstvu) se změní.
      self.options_will_change!
    end
  end

  # Callback pro serializaci všech vrstev do JSONB sloupce 'options' před uložením
  before_save :serialize_all_layers_to_options

  # Inicializace s výchozí strukturou pro nové záznamy
  after_initialize do
    if new_record?
      self.options ||= {} # Zajistí, že options je hash
      LAYER_CONFIGS.each_key do |layer_key|
        attributes_storage_key = "#{layer_key}_attributes"
        self.options[attributes_storage_key] ||= {} # Vytvoří prázdný hash pro každou vrstvu
        # Volitelně můžete zde rovnou zavolat send(layer_key) pro inicializaci PORO s defaulty
      end
    end
  end

  def serialize_all_layers_to_options
    self.options ||= {} # Zajistí, že options je hash, i když bylo původně nil
    LAYER_CONFIGS.each_key do |layer_key|
      layer_object = instance_variable_get("@#{layer_key}") # Získá memoizované PORO

      if layer_object # Pokud bylo PORO pro tuto vrstvu inicializováno (a potenciálně změněno)
        attributes_storage_key = "#{layer_key}_attributes"
        # Uloží atributy z PORO (můžete použít .compact pro odstranění nil hodnot)
        self.options[attributes_storage_key] = layer_object.attributes.compact
      elsif !self.options.key?("#{layer_key}_attributes") && !new_record?
        # Pokud PORO nebylo inicializováno, ale v DB pro něj neexistuje klíč (např. po přidání nové vrstvy do LAYER_CONFIGS)
        # a není to nový záznam, můžeme chtít zajistit, že klíč existuje.
        # Pro nové záznamy to řeší after_initialize.
        # self.options["#{layer_key}_attributes"] = {} # Volitelné: zajistí existenci klíče i pro staré záznamy při prvním uložení
      end
    end
  end

  def hidden?
    hidden_at.present?
  end

  def last?
    page.blocks.last == self
  end

  def first?
    page.blocks.first == self
  end

  def block_name
    heading_text = controls.find { |c| c.type == "BlockControls::Heading" }&.text
    heading_text.present? ? strip_tags(heading_text) : "block-#{id}"
  end

  def to_combobox_display
    block_name
  end

  def has_media?
    media_type.present?
  end
  def has_pricing?
    name == "pricing001"
  end

  # V app/models/block.rb
  def self.initialize_from_block_object(block_object)
    require 'open-uri' # Zvažte přesun na začátek souboru, pokud je používáno i jinde

    # binding.irb # Váš debugovací bod, ponechte pro vlastní ladění

    # 1. Vytvoření nové instance Block
    #    - :options nyní přijímá celý strukturovaný hash z block_object.raw_options,
    #      který zahrnuje i media_layer_attributes.
    #    - :media_options parametr je odstraněn, protože Block model již nemá tento separátní atribut.
    new_block = new( # Přejmenoval jsem lokální proměnnou, aby nekolidovala s metodou `block`
      name: block_object.name,
      page_id: 1, # TODO: Toto je stále hardcoded. Mělo by být dynamické?
      options: block_object.raw_options # Toto nyní obsahuje všechny *_layer_attributes
    # media_options: ... TOTO SE ODSTRAŇUJE
    )

    new_block.tap do |b| # Používáme 'b' pro přehlednost uvnitř tap bloku
      # 2. Zpracování Controls - tato část by měla být víceméně v pořádku
      #    Předpokládáme, že block_object.controls stále vrací pole objektů/hashů,
      #    které mají :type, :options, :text, :position.
      if block_object.controls.present?
        block_object.controls.each do |control_data| # control_data je objekt/hash z block_object.controls
          b.controls.build(
            type: control_data.type,
            options: (control_data.options || {}).deep_symbolize_keys, # Je dobré symbolizovat
            text: control_data.respond_to?(:text) ? control_data.text : nil,
            position: control_data.respond_to?(:position) ? control_data.position : nil
          )
        end
      end

      # 3. Zpracování Media - tato část vyžaduje největší úpravy
      #    block_object.media je instance MediaObject
      #    block_object.media.items je pole instancí MediaItemObject
      #    block_object.media.type je např. "gallery" (z MediaObject.options[:type])
      if block_object.media&.items&.present? # Kontrola, zda existují media a mají položky

        # 3a. Nastavení MediaType na Blocku
        if block_object.media.type.present?
          media_type_record = MediaType.find_by(slug: block_object.media.type)
          b.media_type = media_type_record # Přiřadí asociaci
          # Pokud media_type_record je nil, b.media_type bude nil. Zvažte, zda je to chyba.
        else
          media_type_record = nil # Nebo logování chyby, pokud je typ média povinný
        end

        # Pokračujeme pouze pokud máme platný media_type_record pro vytváření Media AR záznamů
        if media_type_record.present?
          # 3b. Vytvoření MediaCollection (pokud je potřeba)
          media_collection_record = nil
          if media_type_record.respond_to?(:has_groups?) && media_type_record.has_groups?
            # Název kolekce by mohl být dynamičtější
            media_collection_record = MediaCollection.create(
              name: "Kolekce pro blok '#{b.name}' (ID: #{b.id || 'nový'})",
              collection_type: "GalleryMediaCollection", # Toto může být také dynamické
            # media_type: media_type_record
            )
            b.media_collection = media_collection_record
          end

          # 3c. Vytvoření jednotlivých Media ActiveRecord záznamů
          # Logika pro `required_media_count` a `current_media_items` byla pro znovupoužití
          # existujících Media. Pokud chcete vždy vytvářet nové Media pro tento Block,
          # tato logika se zjednoduší. Prozatím předpokládám, že chcete vytvářet nové.
          block_object.media.items.each do |media_item_obj| # media_item_obj je instance MediaItemObject

            # Připravíme atributy pro nový Media AR záznam
            media_attributes = {
              title: media_item_obj.respond_to?(:title) ? media_item_obj.title : "Nepojmenováno",
              content: media_item_obj.respond_to?(:content) ? media_item_obj.content : nil,
              data: media_item_obj.respond_to?(:data) ? (media_item_obj.data || {}).deep_symbolize_keys : {},
              media_type: media_type_record,
              media_collection: media_collection_record
            }

            if media_item_obj.respond_to?(:icon) && media_item_obj.icon.present?
              icon_identifier = media_item_obj.icon.is_a?(String) ? media_item_obj.icon : media_item_obj.icon.try(:name)
              media_attributes[:icon] = Icon.find_by(name: icon_identifier) if icon_identifier.present?
            end

            # Vytvoříme nový Media záznam asociovaný s tímto blokem
            new_media_ar_record = b.media.build(media_attributes)

            # Připojení obrázku
            # Musíme vědět, zda media_item_obj.image je URL nebo již ActiveStorage objekt
            image_source_to_attach = nil
            filename_for_attachment = "image.jpg" # Default filename

            if media_item_obj.respond_to?(:image_url) && media_item_obj.image_url.present?
              # Máme URL, pokusíme se stáhnout
              begin
                image_source_to_attach = URI.open(media_item_obj.image_url)
                filename_for_attachment = File.basename(URI.parse(media_item_obj.image_url).path)
              rescue OpenURI::HTTPError, StandardError => e
                Rails.logger.error "Nepodařilo se stáhnout obrázek z #{media_item_obj.image_url}: #{e.message}"
              end
            elsif media_item_obj.respond_to?(:image_attachment) && media_item_obj.image_attachment.respond_to?(:blob) && media_item_obj.image_attachment.attached?
              # Máme ActiveStorage přílohu (např. pokud BlockObject byl vytvořen z jiného Blocku)
              # Chceme připojit kopii blobu, ne samotný attachment, pokud vytváříme nový Media záznam
              image_source_to_attach = media_item_obj.image_attachment.blob
              filename_for_attachment = media_item_obj.image_attachment.filename.to_s
            end

            if image_source_to_attach.present?
              new_media_ar_record.image.attach(io: image_source_to_attach, filename: filename_for_attachment)
            end

            # new_media_ar_record.save # Není potřeba, pokud `autosave: true` nebo pokud se `new_block.save` zavolá později
          end
        end
      end
    end

    # block.save # Zavolejte .save vně této metody, pokud je toto jen "továrna"
    # Pokud je to metoda typu "Block.create_from_block_object", pak by .save patřilo sem.
    new_block
  end

  def theme
    content_layer.theme
  end

  def content_theme
    content_layer.theme || inner_container_layer.theme_class || outer_container_layer.theme_class
  end
end

