require "test_helper"

class PageTranslatorServiceTest < ActiveSupport::TestCase
  def setup
    @website = websites(:one)
    @original_page = pages(:one)
    @original_page.update!(
      website: @website,
      locale: "cs",
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON> stránka",
      heading: "Původní nadpis",
      meta_title: "Původní meta title",
      meta_description: "Původní meta popis"
    )
    
    # Vytvoř testovací blok s ovládacím prvkem
    @block = Block.create!(
      page: @original_page,
      name: "test_block",
      position: 1,
      options: {
        "content_layer_attributes" => {
          "text" => "Testovací text v bloku"
        }
      }
    )
    
    @control = BlockControls::Heading.create!(
      block: @block,
      text: "Testovací nadpis",
      position: 1,
      options: {
        "heading_type" => "h2",
        "pre_header" => "Přednadpis"
      }
    )
  end

  test "should translate page successfully" do
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert_not_nil translated_page
    assert_equal "en", translated_page.locale
    assert_equal @original_page.id, translated_page.translation_of_id
    assert_equal @original_page.website_id, translated_page.website_id
    
    # Ověř, že texty byly "přeloženy" (s suffixem)
    assert_includes translated_page.title, "_translated_en"
    assert_includes translated_page.heading, "_translated_en"
    assert_includes translated_page.meta_title, "_translated_en"
    assert_includes translated_page.meta_description, "_translated_en"
  end

  test "should duplicate blocks and controls" do
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert_equal @original_page.blocks.count, translated_page.blocks.count
    
    translated_block = translated_page.blocks.first
    assert_equal @block.name, translated_block.name
    assert_equal @block.id, translated_block.translated_from_block_id
    
    # Ověř duplikaci ovládacích prvků
    assert_equal @block.controls.count, translated_block.controls.count
    
    translated_control = translated_block.controls.first
    assert_includes translated_control.text, "_translated_en"
    assert_includes translated_control.options["pre_header"], "_translated_en"
  end

  test "should not translate to same locale" do
    service = PageTranslatorService.new(@original_page.id, "cs")
    translated_page = service.call
    
    assert_nil translated_page
  end

  test "should not translate if translation already exists" do
    # Vytvoř existující překlad
    existing_translation = Page.create!(
      website: @website,
      locale: "en",
      title: "Existing translation",
      translation_of_id: @original_page.id
    )
    
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert_nil translated_page
  end

  test "should handle invalid locale" do
    service = PageTranslatorService.new(@original_page.id, "invalid")
    translated_page = service.call
    
    assert_nil translated_page
  end

  test "should generate unique slug for translation" do
    service = PageTranslatorService.new(@original_page.id, "en")
    translated_page = service.call
    
    assert_not_nil translated_page.slug
    assert_not_equal @original_page.slug, translated_page.slug
  end
end
