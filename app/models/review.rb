# == Schema Information
#
# Table name: reviews
#
#  id           :bigint           not null, primary key
#  approved_at  :datetime
#  content      :text
#  name         :string
#  origin_id    :text
#  origin_name  :string
#  origin_url   :text
#  published_at :datetime
#  rating       :integer
#  removed_at   :datetime
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_reviews_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class Review < ApplicationRecord
  belongs_to :website
  
  validates :name, presence: true
  validates :content, presence: true
  validates :rating, presence: true, inclusion: { in: 1..5 }
  
  scope :approved, -> { where.not(approved_at: nil) }
  scope :published, -> { where.not(published_at: nil) }
  scope :visible, -> { where(removed_at: nil) }
end
