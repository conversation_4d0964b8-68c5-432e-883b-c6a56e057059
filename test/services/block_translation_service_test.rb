require "test_helper"

class BlockTranslationServiceTest < Minitest::Test
  def setup
    # Vytvoř testovací data
    @account = Account.create!(name: "Test Account")
    @website = Website.create!(
      account: @account,
      name: "Test Website",
      email: "<EMAIL>",
      phone: "+************",
      locale: "cs",
      available_locales: ["cs", "en", "de"]
    )

    # Nastav tenant
    ActsAsTenant.current_tenant = @website

    @page = Page.create!(
      website: @website,
      title: "Test stránka",
      locale: "cs",
      type: "Content"
    )
    
    @block = Block.create!(
      name: "test_block",
      options: {}
    )
    
    # Vytvoř page-block vztah
    PageBlock.create!(
      page: @page,
      block: @block,
      position: 1
    )
    
    @control = BlockControl.create!(
      block: @block,
      type: "BlockControls::Heading",
      text: "Původní český text",
      position: 1,
      options: { "heading_type" => "h2" }
    )
  end

  def teardown
    ActsAsTenant.without_tenant do
      PageBlock.destroy_all
      BlockControl.destroy_all
      Block.destroy_all
      Page.destroy_all
      Website.destroy_all
      Account.destroy_all
    end
  end

  def test_should_translate_block_control_text
    service = BlockTranslationService.new(@block, "en", "cs")
    result = service.call
    
    assert result, "Překlad by měl být úspěšný"
    
    # Zkontroluj, že text byl přeložen
    Mobility.with_locale("en") do
      @control.reload
      translated_text = @control.text
      
      assert translated_text.present?, "Přeložený text by měl existovat"
      refute_equal "Původní český text", translated_text, "Text by měl být přeložen"
      
      puts "✅ Text přeložen: '#{@control.text_translations['cs']}' -> '#{translated_text}'"
    end
  end

  def test_should_preserve_original_text
    service = BlockTranslationService.new(@block, "en", "cs")
    service.call
    
    # Zkontroluj, že původní text zůstal zachován
    Mobility.with_locale("cs") do
      @control.reload
      assert_equal "Původní český text", @control.text
    end
  end

  def test_should_handle_multiple_locales
    # Přelož do angličtiny
    service_en = BlockTranslationService.new(@block, "en", "cs")
    service_en.call
    
    # Přelož do němčiny
    service_de = BlockTranslationService.new(@block, "de", "cs")
    service_de.call
    
    @control.reload
    
    # Zkontroluj všechny překlady
    assert_equal "Původní český text", @control.text_translations["cs"]
    assert @control.text_translations["en"].present?
    assert @control.text_translations["de"].present?
    
    puts "✅ Více jazyků: #{@control.text_translations.keys.sort}"
  end

  def test_available_translations
    service = BlockTranslationService.new(@block, "en", "cs")
    
    # Před překladem
    assert_equal [], service.available_translations.sort
    
    # Po překladu
    service.call
    available = service.available_translations.sort
    
    assert_includes available, "en"
    puts "✅ Dostupné překlady: #{available}"
  end

  def test_translation_stats
    service = BlockTranslationService.new(@block, "en", "cs")
    service.call
    
    stats = service.translation_stats
    
    assert stats["en"].present?
    assert_equal 1, stats["en"][:controls]
    assert stats["en"][:characters] > 0
    
    puts "✅ Statistiky překladů: #{stats}"
  end

  def test_should_not_translate_same_locale
    service = BlockTranslationService.new(@block, "cs", "cs")
    result = service.call
    
    refute result, "Překlad do stejného jazyka by neměl být povolen"
  end

  def test_should_handle_empty_text
    @control.update!(text: "")
    
    service = BlockTranslationService.new(@block, "en", "cs")
    result = service.call
    
    refute result, "Překlad prázdného textu by neměl být povolen"
  end

  def test_block_can_be_shared_between_pages
    # Vytvoř druhou stránku
    page2 = Page.create!(
      website: @website,
      title: "Druhá stránka",
      locale: "en",
      type: "Content"
    )
    
    # Přidej stejný blok na druhou stránku
    PageBlock.create!(
      page: page2,
      block: @block,
      position: 1
    )
    
    # Zkontroluj, že blok je sdílený
    assert_equal 2, @block.pages.count
    assert_includes @block.pages, @page
    assert_includes @block.pages, page2
    
    puts "✅ Blok je sdílený mezi #{@block.pages.count} stránkami"
  end
end
