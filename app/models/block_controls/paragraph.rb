# == Schema Information
#
# Table name: block_controls
#
#  id                      :bigint           not null, primary key
#  classes                 :jsonb
#  container               :string
#  content                 :jsonb
#  options                 :jsonb
#  position                :integer          default(0)
#  pre_header_translations :jsonb
#  styles                  :jsonb
#  text                    :text
#  text_translations       :jsonb
#  type                    :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  block_id                :bigint
#
# Indexes
#
#  index_block_controls_on_block_id                 (block_id)
#  index_block_controls_on_pre_header_translations  (pre_header_translations) USING gin
#  index_block_controls_on_text_translations        (text_translations) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class BlockControls::Paragraph < BlockControl
  before_save :json_parse_content

  def json_parse_content
    self.content = JSON.parse(self.content) unless self.content.is_a?(Hash)
  end

  def self.permitted_attributes
    [ :id, :content, :text, classes: {} ]
  end

  def self.sti_name
    'Paragraph'
  end
end
