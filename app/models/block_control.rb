# == Schema Information
#
# Table name: block_controls
#
#  id                :bigint           not null, primary key
#  classes           :jsonb
#  container         :string
#  content           :jsonb
#  options           :jsonb
#  position          :integer          default(0)
#  styles            :jsonb
#  text              :text
#  text_translations :jsonb
#  type              :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  block_id          :bigint
#
# Indexes
#
#  index_block_controls_on_block_id           (block_id)
#  index_block_controls_on_text_translations  (text_translations) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class BlockControl < ApplicationRecord
  include TailwindClassable

  belongs_to :block

  validates :type, presence: true

  positioned on: %i[block_id]

  store_accessor :options
  store_accessor :classes
  store_accessor :styles
  store_accessor :content

  # Mobility gem pro překlady textů
  extend Mobility
  translates :text, backend: :jsonb

  # store_accessor :options, :heading_type, :primary_button_text, :primary_button_link, :secondary_button_text, :secondary_button_link

  default_scope { order(position: :asc) }

  scope :visible, -> { where(hidden_at: nil) }

  def edit_template_file
    "admin/content/blocks/controls/#{self.type.split('::').last.downcase}"
  end
end
